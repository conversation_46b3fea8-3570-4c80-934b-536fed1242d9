package com.thyview.ui.reviews

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Star
import androidx.compose.material.icons.filled.StarBorder
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.thyview.ui.ThyViewTheme
import com.thyview.ui.ImdbYellow
import com.thyview.ui.ImdbWhite
import com.thyview.ui.ImdbMediumGray

/**
 * An interactive rating stars component that allows users to select a rating
 *
 * @param rating The current rating value (0-5)
 * @param onRatingChanged Callback when the rating is changed
 * @param maxStars The maximum number of stars to display, defaults to 5
 * @param starSize The size of each star icon in dp
 * @param starSpacing The space between stars in dp
 * @param filledStarColor The color of filled stars
 * @param unfilledStarColor The color of unfilled stars
 * @param showRatingText Whether to display the numeric rating text
 * @param modifier Optional modifier for the component
 */
@Composable
fun InteractiveRatingStars(
    rating: Int,
    onRatingChanged: (Int) -> Unit,
    maxStars: Int = 5,
    starSize: Int = 32,
    starSpacing: Int = 4,
    filledStarColor: Color = ImdbYellow,
    unfilledStarColor: Color = ImdbMediumGray,
    showRatingText: Boolean = true,
    modifier: Modifier = Modifier
) {
    // Ensure the rating is clamped between 0 and maxStars
    val clampedRating = rating.coerceIn(0, maxStars)

    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
    ) {
        // Draw the stars
        for (i in 1..maxStars) {
            val isFilled = i <= clampedRating
            
            Icon(
                imageVector = if (isFilled) Icons.Default.Star else Icons.Default.StarBorder,
                contentDescription = "Star $i",
                tint = if (isFilled) filledStarColor else unfilledStarColor,
                modifier = Modifier
                    .size(starSize.dp)
                    .clickable { onRatingChanged(i) }
            )

            // Add spacing between stars except after the last one
            if (i < maxStars) {
                Spacer(modifier = Modifier.width(starSpacing.dp))
            }
        }

        // Optionally show the numeric rating
        if (showRatingText) {
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = if (clampedRating > 0) "$clampedRating/$maxStars" else "Tap to rate",
                color = ImdbWhite,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

// Preview for the InteractiveRatingStars component
@Preview(showBackground = true)
@Composable
fun InteractiveRatingStarsPreview() {
    ThyViewTheme {
        Surface(
            color = MaterialTheme.colorScheme.surface,
            modifier = Modifier.padding(16.dp)
        ) {
            Column(
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                var rating1 by remember { mutableStateOf(0) }
                var rating2 by remember { mutableStateOf(3) }
                var rating3 by remember { mutableStateOf(5) }

                Text(
                    text = "Interactive Rating Stars Examples",
                    style = MaterialTheme.typography.headlineSmall,
                    color = ImdbWhite
                )

                // No rating selected
                InteractiveRatingStars(
                    rating = rating1,
                    onRatingChanged = { rating1 = it },
                    showRatingText = true
                )

                // 3 stars selected
                InteractiveRatingStars(
                    rating = rating2,
                    onRatingChanged = { rating2 = it },
                    showRatingText = true
                )

                // 5 stars selected
                InteractiveRatingStars(
                    rating = rating3,
                    onRatingChanged = { rating3 = it },
                    showRatingText = true
                )

                // Custom styling example
                var rating4 by remember { mutableStateOf(2) }
                InteractiveRatingStars(
                    rating = rating4,
                    onRatingChanged = { rating4 = it },
                    maxStars = 5,
                    starSize = 40,
                    starSpacing = 6,
                    filledStarColor = ImdbYellow,
                    unfilledStarColor = ImdbMediumGray,
                    showRatingText = true
                )
            }
        }
    }
}
