package com.thyview.ui.reviews

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.thyview.models.reviews.Comment
import com.thyview.models.reviews.Post
import com.thyview.ui.ThyViewTheme
import com.thyview.ui.ImdbBlack

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PostDetailScreen(
    postId: String,
    navController: NavController,
    viewModel: CommentViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()

    ThyViewTheme {
        Scaffold(
            topBar = {
                TopAppBar(
                    title = { Text("Post Details") },
                    navigationIcon = {
                        IconButton(onClick = { navController.navigateUp() }) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "Back"
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = ImdbBlack,
                        titleContentColor = Color.White,
                        navigationIconContentColor = Color.White
                    )
                )
            },
            containerColor = ImdbBlack
        ) { paddingValues ->
            LaunchedEffect(postId) {
                viewModel.loadPostAndComments()
            }

            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(bottom = 72.dp) // Add padding for the input bar
                ) {
                    uiState.post?.let {
                        PostItemCard(
                            post = it,
                            onLike = { viewModel.onLike(it.id) },
                            onComment = {
                                // Clear any existing text and prepare for new comment
                                viewModel.onCommentTextChange("")
                                // In a real implementation, you might also want to focus the comment input
                                // or scroll to the comment section
                            }
                        )
                    } ?: run {
                        // Show loading state when post is null
                        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                            CircularProgressIndicator()
                        }
                    }
                    Divider(color = Color.DarkGray) // Darken the divider to match the theme
                    LazyColumn(modifier = Modifier.weight(1f)) {
                        items(uiState.comments) { comment ->
                            Column {
                                CommentItemCard(
                                    comment = comment,
                                    repliesCount = comment.replyCount ?: 0,
                                    onReply = {
                                        // Navigate to replies screen when Reply is clicked
                                        navController.navigate("replies/${postId}/${comment.id}")
                                    },
                                    onViewReplies = {
                                        // Navigate to replies screen when viewing replies
                                        navController.navigate("replies/${postId}/${comment.id}")
                                    }
                                )
                                Divider(
                                    color = Color.DarkGray,
                                    modifier = Modifier.padding(horizontal = 18.dp)
                                )
                            }
                        }
                    }
                }

                CommentInputBar(
                    value = uiState.commentText,
                    onValueChange = viewModel::onCommentTextChange,
                    onSend = { text, imageUri ->
                        viewModel.submitComment()
                    },
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .fillMaxWidth()
                )
            }
        }
    }
}

/**
 * Preview for the PostDetailScreen with sample data
 */
@OptIn(ExperimentalMaterial3Api::class)
@Preview(showBackground = true)
@Composable
fun PostDetailScreenPreview() {
    // Create sample data
    val samplePost = Post(
        id = "sample-post-1",
        externalAuthorId = "JohnDoe",
        profileImageUrl = "",
        text = "I had an amazing time at this restaurant.",
        imageUrl = null,
        username = "user1",
        likeCount = 15,
        commentCount = 1,
        createdAt = "2 hours ago",
        liked = false
    )
    
    val sampleComments = listOf(
        Comment(
            id = "comment-1",
            externalAuthorId = "Alice",
            text = "I agree! Their pasta is incredible.",
            createdAt = "1 hour ago",
            username = "user1",
            replyCount = 3
        )
    )
    
    // Use ThyViewTheme for consistent styling
    ThyViewTheme {
        Scaffold(
            topBar = {
                TopAppBar(
                    title = { Text("Post Details") },
                    navigationIcon = {
                        IconButton(onClick = { /* Would navigate back in real implementation */ }) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "Back"
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = ImdbBlack,
                        titleContentColor = Color.White,
                        navigationIconContentColor = Color.White
                    )
                )
            },
            containerColor = ImdbBlack
        ) { paddingValues ->
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(bottom = 72.dp) // Add padding for the input bar
                ) {
                    PostItemCard(
                        post = samplePost,
                        onLike = { },
                        onComment = { }
                    )
                    Divider(color = Color.DarkGray) // Darken the divider to match the theme
                    LazyColumn(modifier = Modifier.weight(1f)) {
                        items(sampleComments) { comment ->
                            CommentItemCard(
                                comment = comment,
                                repliesCount = comment.replyCount,
                                onReply = { 
                                    // Would navigate to replies in a real implementation
                                },
                                onViewReplies = { 
                                    // Would navigate to replies in a real implementation
                                }
                            )
                        }
                    }
                }
                
                // Input bar stays at the bottom
                CommentInputBar(
                    value = "",
                    onValueChange = { },
                    onSend = { _, _ -> },
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .fillMaxWidth()
                )
            }
        }
    }
}
