package com.thyview.ui.reviews

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.thyview.services.AWSPostService
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * ViewModel for adding reviews using the existing createPost API
 */
@HiltViewModel
class AddReviewViewModel @Inject constructor(
    private val postService: AWSPostService
) : ViewModel() {

    private val _uiState = MutableStateFlow(AddReviewUiState())
    val uiState: StateFlow<AddReviewUiState> = _uiState.asStateFlow()

    /**
     * Update review text
     */
    fun updateReviewText(text: String) {
        _uiState.update { it.copy(reviewText = text, error = null) }
    }

    /**
     * Submit review using the existing createPost API
     */
    fun submitReview(movieTitle: String? = null) {
        val currentState = _uiState.value
        
        if (currentState.reviewText.length < 300) {
            _uiState.update { it.copy(error = "Review must be at least 300 characters long") }
            return
        }
        
        if (currentState.isSubmitting) return

        viewModelScope.launch {
            _uiState.update { it.copy(isSubmitting = true, error = null) }
            
            try {
                // Format the review text with movie title if provided
                val reviewText = if (movieTitle != null) {
                    "Review for \"$movieTitle\":\n\n${currentState.reviewText}"
                } else {
                    currentState.reviewText
                }
                
                // Use the existing createPost API
                val postId = postService.createPost(
                    text = reviewText,
                    imageUrl = null
                )
                
                if (postId != null) {
                    _uiState.update { 
                        it.copy(
                            isSubmitting = false,
                            isSubmitted = true,
                            error = null
                        ) 
                    }
                    
                    Timber.d("Review submitted successfully with post ID: $postId")
                } else {
                    _uiState.update { 
                        it.copy(
                            isSubmitting = false,
                            error = "Failed to submit review. Please try again."
                        ) 
                    }
                    
                    Timber.e("Failed to submit review - createPost returned null")
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isSubmitting = false,
                        error = e.message ?: "An unexpected error occurred"
                    ) 
                }
                
                Timber.e(e, "Error submitting review")
            }
        }
    }

    /**
     * Reset the submission state (useful for navigation)
     */
    fun resetSubmissionState() {
        _uiState.update { it.copy(isSubmitted = false) }
    }
}

/**
 * UI state for add review screen
 */
data class AddReviewUiState(
    val reviewText: String = "",
    val isSubmitting: Boolean = false,
    val isSubmitted: Boolean = false,
    val error: String? = null
)
