package com.thyview.ui.articles

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.outlined.FavoriteBorder
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import com.thyview.ui.ImdbDarkGray
import com.thyview.ui.ThyViewTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.thyview.models.articles.Article
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

@Composable
fun ArticleItem(
    article: Article,
    onArticleClick: (Article) -> Unit,
    onLikeClick: (Article) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onArticleClick(article) }
            .padding(vertical = 8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(
            containerColor = ImdbDarkGray
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Article image
            article.imageUrl?.let { imageUrl ->
                AsyncImage(
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(imageUrl)
                        .crossfade(true)
                        .build(),
                    contentDescription = article.title,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(180.dp)
                        .clip(RoundedCornerShape(8.dp))
                )
                Spacer(modifier = Modifier.height(12.dp))
            }
            
            // Article title
            Text(
                text = article.title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // Article description
            Text(
                text = article.description,
                style = MaterialTheme.typography.bodyMedium,
                maxLines = 3,
                overflow = TextOverflow.Ellipsis
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Bottom row with metadata and like button
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Author and date info
                Column {
                    article.postedBy?.let { author ->
                        if (author.isNotEmpty()) {
                            Text(
                                text = author,
                                style = MaterialTheme.typography.bodySmall,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                    
                    Text(
                        text = formatDate(article.createdAt),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                // Like button
                IconButton(onClick = { onLikeClick(article) }) {
                    Icon(
                        imageVector = if (article.isLikedByUser) Icons.Filled.Favorite else Icons.Outlined.FavoriteBorder,
                        contentDescription = if (article.isLikedByUser) "Unlike" else "Like",
                        tint = if (article.isLikedByUser) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

private fun formatDate(dateString: String): String {
    return try {
        val inputFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.getDefault())
        val outputFormat = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
        val date = inputFormat.parse(dateString)
        outputFormat.format(date ?: Date())
    } catch (e: Exception) {
        dateString
    }
}

@Preview(showBackground = true, widthDp = 360)
@Composable
fun ArticleItemPreview() {
    val sampleArticle = Article(
        id = "1",
        title = "Thyroid Health: Understanding Hypothyroidism",
        description = "Learn about the symptoms, causes, and treatments for hypothyroidism, a common thyroid disorder affecting millions worldwide.",
        imageUrl = "https://example.com/image.jpg",
        contentUrl = "https://example.com/content.html",
        likes = 42,
        createdAt = "2023-05-15T12:00:00Z",
        modifiedAt = "2023-05-16T10:30:00Z",
        postedBy = "Dr. Jane Smith",
        additionalNotes = "Additional notes",
        content = null,
        tags = listOf("thyroid", "health", "hypothyroidism"),
        isLikedByUser = true
    )
    
    ThyViewTheme {
        ArticleItem(
            article = sampleArticle,
            onArticleClick = {},
            onLikeClick = {}
        )
    }
}

@Preview(showBackground = true, widthDp = 360)
@Composable
fun ArticleItemWithoutImagePreview() {
    val sampleArticle = Article(
        id = "2",
        title = "Latest Advances in Hyperthyroidism Treatment Options",
        description = "Discover the newest treatments available for managing hyperthyroidism, including medications, radioactive iodine, and surgical approaches.",
        imageUrl = null,
        contentUrl = "https://example.com/article/2",
        likes = 24,
        createdAt = "2023-07-02T09:30:00Z",
        modifiedAt = "2023-07-03T14:15:00Z",
        postedBy = "Dr. Michael Johnson",
        additionalNotes = "",
        content = null,
        tags = listOf("thyroid", "hyperthyroidism", "treatment"),
        isLikedByUser = false
    )
    
    ThyViewTheme {
        ArticleItem(
            article = sampleArticle,
            onArticleClick = {},
            onLikeClick = {}
        )
    }
}
