
package com.thyview.services.articles

import com.thyview.models.articles.Article
import com.thyview.models.articles.ArticleLikeRequest
import com.thyview.models.articles.ArticleLikeResponse
import com.thyview.models.articles.ArticlesData
import com.thyview.models.articles.ArticlesResponse
import java.time.Instant
import javax.inject.Inject

/**
 * Implementation of the ArticlesApiService interface that works with the Retrofit-generated
 * API client to handle article-related API operations.
 */
class ArticlesApiServiceImpl @Inject constructor(
    private val retrofitArticlesApiService: ArticlesApiService
) : ArticlesApiService {

    val useMockData = true

    // Mock articles with Telugu cinema content
    private val mockArticles = listOf(
        Article(
            id = "article1",
            title = "RRR: The Global Phenomenon of Telugu Cinema",
            description = "How S.S<PERSON> Rajamouli's epic period action drama 'RRR' broke barriers and gained international recognition.",
            imageUrl = "https://assets-in.bmscdn.com/iedb/movies/images/mobile/thumbnail/xlarge/rrr-et00094579-12-05-2022-02-18-43.jpg",
            contentUrl = "https://www.imdb.com/title/tt8178634/",
            likes = 352,
            createdAt = "2023-07-03", // 1 week ago
            modifiedAt = Instant.now().minusSeconds(432000).toString(), // 5 days ago
            postedBy = "TollywoodInsider",
            additionalNotes = "Ram Charan and Jr. NTR starrer that won an Oscar for Best Original Song",
            content = "RRR (Rise, Roar, Revolt) is a 2022 Indian Telugu-language epic action drama film directed by S. S. Rajamouli. The film stars N. T. Rama Rao Jr., Ram Charan, Ajay Devgn, Alia Bhatt, Shriya Saran, Samuthirakani, Ray Stevenson, Alison Doody, and Olivia Morris. It centers around the fictional versions of two Indian revolutionaries, Alluri Sitarama Raju (Charan) and Komaram Bheem (Rama Rao), their friendship, and their fight against the British Raj.\n\nThe film has received international acclaim and has broken numerous box office records, becoming one of the highest-grossing Indian films of all time. It won the Academy Award for Best Original Song for 'Naatu Naatu', marking the first win for an Indian production in this category.",
            tags = listOf("RRR", "Rajamouli", "Telugu Cinema", "Period Drama", "International", "Oscar"),
            isLikedByUser = true
        ),
        Article(
            id = "article2",
            title = "The Evolution of Mahesh Babu's Career",
            description = "Exploring how Mahesh Babu transformed from a child actor to one of Telugu cinema's biggest superstars.",
            imageUrl = "",
            contentUrl = "https://example.com/articles/mahesh-babu-career",
            likes = 247,
            createdAt = "2023-07-03", // 2 weeks ago
            modifiedAt = null,
            postedBy = "CinemaSpotlight",
            additionalNotes = "Known as 'Prince' in Telugu cinema",
            content = "Mahesh Babu, born Ghattamaneni Mahesh Babu, is one of the most prominent actors in Telugu cinema. Beginning his career as a child artist, Mahesh has evolved into a leading actor known for his versatile performances and charismatic screen presence.\n\nHis breakthrough came with the film 'Murari' in 2001, followed by the critically acclaimed 'Okkadu' in 2003. Over the years, he has starred in numerous commercial successes like 'Pokiri', 'Dookudu', 'Srimanthudu', and 'Bharat Ane Nenu'. His films are known for their social messages wrapped in commercial packages.\n\nMahesh is also known for his philanthropic efforts through the Mahesh Babu Foundation and his clean, non-controversial public image. His dedication to choosing diverse roles has earned him the reputation of being one of the most bankable stars in the Telugu film industry.",
            tags = listOf("Mahesh Babu", "Telugu Cinema", "Actor Profile", "Superstar", "Career Evolution"),
            isLikedByUser = false
        ),
        Article(
            id = "article3",
            title = "Baahubali: The Film That Redefined Indian Cinema",
            description = "How the Baahubali franchise changed the landscape of Indian filmmaking and set new benchmarks.",
            imageUrl = "https://images.hindustantimes.com/img/2022/02/16/1600x900/Baahubali-_The_Beginning_1645005414757_1645005435429.jpg",
            contentUrl = "https://example.com/articles/baahubali-impact",
            likes = 419,
            createdAt = "2023-07-03", // 4 weeks ago
            modifiedAt = Instant.now().minusSeconds(1814400).toString(), // 3 weeks ago
            postedBy = "TollywoodInsider",
            additionalNotes = "The highest-grossing Indian film series of all time until recently",
            content = "The Baahubali franchise, consisting of 'Baahubali: The Beginning' (2015) and 'Baahubali: The Conclusion' (2017), directed by S.S. Rajamouli, is a landmark in Indian cinema. These epic historical fiction films starred Prabhas, Rana Daggubati, Anushka Shetty, Tamannaah, Ramya Krishna, and Sathyaraj.\n\nThe films are set in the fictional kingdom of Mahishmati and follow the rivalry between cousins Amarendra Baahubali and Bhallaladeva. Known for their groundbreaking visual effects, grand scale storytelling, and massive production values, the Baahubali films shattered box office records and became a pan-Indian phenomenon.\n\nBaahubali changed how Indian films are perceived globally and demonstrated that regional cinema could achieve nationwide and international success. It expanded the horizons for filmmakers across the country and inspired many to think beyond traditional boundaries of scale and storytelling.",
            tags = listOf("Baahubali", "Rajamouli", "Prabhas", "Telugu Cinema", "Epic", "Franchise"),
            isLikedByUser = true
        ),
        Article(
            id = "article4",
            title = "Exploring Chiranjeevi's Legacy in Telugu Cinema",
            description = "A look at Megastar Chiranjeevi's influential career spanning over four decades.",
            imageUrl = "https://www.telugubulletin.com/wp-content/uploads/2023/05/chiranjeevi-4.jpg",
            contentUrl = "https://example.com/articles/chiranjeevi-legacy",
            likes = 378,
            createdAt = "2023-07-03", // 6 weeks ago
            modifiedAt = null,
            postedBy = "FilmHistorian",
            additionalNotes = "Chiranjeevi has appeared in more than 150 films",
            content = "Konidela Siva Sankara Vara Prasad, known professionally as Chiranjeevi, is a towering figure in Telugu cinema. Since his debut in 1978, he has become one of the most influential actors in the history of Indian cinema.\n\nChiranjeevi is credited with bringing a new dimension to action choreography in Telugu cinema. His dancing skills, action sequences, and charismatic screen presence revolutionized the industry in the 1980s and 1990s. Films like 'Khaidi', 'Gang Leader', 'Indra', and 'Tagore' established him as a versatile actor capable of handling various genres.\n\nBeyond cinema, Chiranjeevi ventured into politics, founding the Praja Rajyam Party in 2008. Despite his political career, he returned to acting with films like 'Khaidi No. 150' and 'Sye Raa Narasimha Reddy'. His impact extends beyond his own career, as he is the patriarch of a family that includes several successful actors including his brother Pawan Kalyan and son Ram Charan.",
            tags = listOf("Chiranjeevi", "Megastar", "Telugu Cinema", "Legacy", "Acting Career", "Film History"),
            isLikedByUser = false
        ),
        Article(
            id = "article5",
            title = "The Rise of Allu Arjun as a Pan-Indian Star",
            description = "How 'Pushpa: The Rise' catapulted Allu Arjun to nationwide fame and changed his career trajectory.",
            imageUrl = "https://filmfare.wwmindia.com/content/2022/mar/alluarjunpushpa21646043451.jpg",
            contentUrl = "https://example.com/articles/allu-arjun-pan-indian",
            likes = 286,
            createdAt = "2023-07-03", // 6 days ago
            modifiedAt = Instant.now().minusSeconds(172800).toString(), // 2 days ago
            postedBy = "MovieAnalyst",
            additionalNotes = "Known for his unique dancing style and dialogues",
            content = "Allu Arjun, often referred to as 'Stylish Star' by his fans, has transformed from being a Telugu cinema icon to a nationwide sensation. His journey from films like 'Arya' and 'Bunny' to becoming a pan-Indian star with 'Pushpa: The Rise' is remarkable.\n\nKnown for his extraordinary dancing skills and versatile acting, Allu Arjun has consistently reinvented himself throughout his career. Films like 'Ala Vaikunthapurramuloo' showcased his mass appeal, while 'Pushpa: The Rise' demonstrated his ability to portray raw, rugged characters with authenticity.\n\n'Pushpa: The Rise' became a cultural phenomenon across India, with Arjun's dialogues, mannerisms, and the 'Srivalli' hook step becoming immensely popular nationwide. The film's success in Hindi-speaking regions, despite no promotional efforts, highlights Arjun's growing appeal beyond traditional Telugu cinema markets.\n\nAs Allu Arjun prepares for 'Pushpa 2: The Rule', expectations are sky-high, and his trajectory suggests he's poised to become one of Indian cinema's biggest stars with a truly pan-Indian appeal.",
            tags = listOf("Allu Arjun", "Pushpa", "Pan-Indian", "Star", "Telugu Cinema", "Stylish Star"),
            isLikedByUser = true
        ),
        Article(
            id = "article6",
            title = "The New Wave of Telugu Cinema: Content-Driven Storytelling",
            description = "Exploring how a new generation of filmmakers is focusing on unique stories and fresh narratives in Telugu films.",
            imageUrl = "https://www.filmibeat.com/ph-big/2019/07/c-o-kancharapalem_156287484210.jpg",
            contentUrl = "https://example.com/articles/new-wave-telugu-cinema",
            likes = 198,
            createdAt = "2023-07-03", // 3 days ago
            modifiedAt = null,
            postedBy = "FilmCritic",
            additionalNotes = "Featuring directors like Nag Ashwin, Venkatesh Maha, and Vivek Athreya",
            content = "Telugu cinema is experiencing a significant shift with a new wave of filmmakers prioritizing content-driven storytelling over star power. Directors like Nag Ashwin ('Mahanati'), Venkatesh Maha ('C/o Kancharapalem'), Vivek Athreya ('Ante Sundaraniki'), and Tharun Bhascker ('Pelli Choopulu') are leading this change.\n\nThese filmmakers are exploring realistic narratives, complex characters, and unconventional storylines that resonate with today's audiences. Films like 'Care of Kancharapalem', 'Pellichoopulu', 'Agent Sai Srinivasa Athreya', and 'Middle Class Melodies' have proven that commercial success is possible without adhering to traditional formulaic approaches.\n\nThe rise of OTT platforms has further encouraged this trend, providing these filmmakers with alternative avenues to reach audiences. The success of films like 'Mathu Vadalara' and 'Cinema Bandi' on streaming platforms has validated this approach.\n\nWhile mainstream commercial films continue to dominate the box office, this parallel movement of content-driven cinema is gradually changing audience expectations and industry standards, promising a more diverse future for Telugu cinema.",
            tags = listOf("New Wave", "Content-Driven", "Telugu Cinema", "Independent", "Filmmakers", "Storytelling"),
            isLikedByUser = false
        ),
        Article(
            id = "article7",
            title = "Prabhas: From Regional Star to National Icon",
            description = "Tracing Prabhas's journey from his early days to becoming one of India's biggest stars post-Baahubali.",
            imageUrl = "https://www.koimoi.com/wp-content/new-galleries/2022/10/prabhas-charges-150-crores-for-adipurush-001.jpg",
            contentUrl = "https://example.com/articles/prabhas-journey",
            likes = 325,
            createdAt = "2023-07-03", // 1 day ago
            modifiedAt = null,
            postedBy = "CinemaSpotlight",
            additionalNotes = "Known as 'Rebel Star' in Telugu cinema",
            content = "Uppalapati Venkata Suryanarayana Prabhas Raju, known simply as Prabhas, has had one of the most remarkable career trajectories in Indian cinema. From being a regional star in Telugu films to becoming a nationwide sensation after 'Baahubali', his journey exemplifies how talent transcends language barriers.\n\nPrabhas began his career with 'Eeswar' in 2002, gradually establishing himself with films like 'Varsham', 'Chatrapathi', and 'Darling'. His collaboration with director S.S. Rajamouli for 'Baahubali' changed everything. The two-part epic fantasy became a phenomenon across India and internationally, with Prabhas dedicating five years exclusively to this project.\n\nPost-Baahubali, Prabhas became one of the few actors from Telugu cinema to achieve true pan-Indian stardom. His subsequent films like 'Saaho', 'Radhe Shyam', and 'Adipurush' have been multi-lingual releases targeting the entire Indian market. Projects like 'Project K' with Deepika Padukone and 'Salaar' with KGF-fame director Prashanth Neel highlight his current stature in the industry.\n\nWhat sets Prabhas apart is his humble personality despite massive stardom, his willingness to take risks, and his dedication to his craft - qualities that have endeared him to audiences across India.",
            tags = listOf("Prabhas", "Baahubali", "Pan-Indian", "Telugu Cinema", "Career", "Stardom"),
            isLikedByUser = true
        ),
        Article(
            id = "article8",
            title = "Tollywood's Impact on Indian Cinema's Global Recognition",
            description = "How Telugu cinema has contributed to raising the profile of Indian films on the international stage.",
            imageUrl = "https://www.news18.com/static-assets/images/telugu-cinema-lead.jpg",
            contentUrl = "https://example.com/articles/tollywood-global-impact",
            likes = 210,
            createdAt = "2023-07-03", // 8 weeks ago
            modifiedAt = Instant.now().minusSeconds(2592000).toString(), // 1 month ago
            postedBy = "GlobalFilmAnalyst",
            additionalNotes = "RRR's Oscar win highlighted Telugu cinema globally",
            content = "Telugu cinema, often referred to as Tollywood, has emerged as a significant contributor to Indian cinema's growing global footprint. The industry's evolution from regional significance to international recognition represents a remarkable journey.\n\nThe watershed moment came with S.S. Rajamouli's 'Baahubali' franchise, which not only dominated the Indian box office but also made waves internationally, grossing over $100 million worldwide. This success demonstrated that Indian films with regional roots could achieve global appeal when backed by universal storytelling and technical excellence.\n\nThe trend continued with 'RRR', which became a worldwide phenomenon, especially after its Netflix release. The film's win at the 95th Academy Awards for Best Original Song ('Naatu Naatu') marked a historic achievement not just for Telugu cinema but for Indian cinema as a whole.\n\nTollywood's technical prowess, particularly in action choreography, visual effects, and grand-scale production design, has raised the bar for Indian cinema. Directors like Rajamouli have shown that Indian filmmakers can create spectacles that rival Hollywood productions while maintaining cultural authenticity.\n\nThe industry's ability to blend traditional storytelling with contemporary technical excellence has captured the attention of international audiences and critics alike, paving the way for greater recognition of Indian cinema beyond stereotypical perceptions.",
            tags = listOf("Tollywood", "Global", "International", "Telugu Cinema", "Oscar", "Recognition"),
            isLikedByUser = false
        ),
        Article(
            id = "article9",
            title = "The Role of Music in Telugu Cinema Success",
            description = "Analyzing how music composers like M.M. Keeravani, Devi Sri Prasad, and Thaman have shaped Telugu film narratives.",
            imageUrl = "https://m.media-amazon.com/images/M/MV5BMTEwMjJmZDQtNDBkMS00ZjU5LWE1ZWYtMWFlMWY3MjBiNGQxXkEyXkFqcGdeQXVyMTEzNzg0Mjkx._V1_.jpg",
            contentUrl = "https://example.com/articles/telugu-cinema-music",
            likes = 176,
            createdAt = "2023-07-03", // 4 days ago
            modifiedAt = null,
            postedBy = "MusicAnalyst",
            additionalNotes = "Telugu film songs often go viral nationwide",
            content = "Music has always been integral to Telugu cinema, serving not just as entertainment but as a crucial storytelling element. The industry has produced exceptional music composers who have contributed significantly to the success and popularity of Telugu films.\n\nM.M. Keeravani (also known as M.M. Kreem in Hindi cinema) has been a revolutionary force with his work in films like 'Baahubali' and 'RRR'. His Oscar-winning composition 'Naatu Naatu' showcased how Telugu film music can achieve global recognition while retaining its cultural roots.\n\nDevi Sri Prasad (DSP) has mastered the art of creating mass appeal with his infectious beats and folk influences. His work in films like 'Pushpa', 'Rangasthalam', and numerous Allu Arjun starrers has resulted in chart-topping hits that transcend language barriers.\n\nS.S. Thaman has revitalized mass commercial cinema music with his energetic compositions for films like 'Ala Vaikunthapurramuloo', whose soundtrack became a nationwide sensation. Similarly, composers like Anirudh Ravichander, who crossed over from Tamil cinema, have brought fresh sounds to Telugu films.\n\nThe impact of these composers extends beyond the films themselves, with songs often going viral nationally through social media and contributing significantly to a film's marketing and cultural impact. As Telugu cinema continues to expand its reach, its distinctive musical identity remains one of its strongest assets.",
            tags = listOf("Music", "Keeravani", "DSP", "Thaman", "Telugu Cinema", "Soundtracks", "Naatu Naatu"),
            isLikedByUser = true
        ),
        Article(
            id = "article10",
            title = "The Rise of OTT Platforms and Their Impact on Telugu Cinema",
            description = "How streaming services are changing content creation and consumption patterns in Telugu film industry.",
            imageUrl = "https://img.theweek.in/content/dam/week/news/entertainment/images/2020/6/1/aha-ott-platform.jpg",
            contentUrl = "https://example.com/articles/telugu-ott-impact",
            likes = 153,
            createdAt = "2023-07-03", // 2 days ago
            modifiedAt = null,
            postedBy = "DigitalMediaExpert",
            additionalNotes = "Platforms like Netflix, Amazon Prime, and Aha are changing the game",
            content = "The proliferation of OTT (Over-The-Top) platforms has significantly transformed the Telugu cinema landscape, creating new opportunities for filmmakers and expanding audience reach.\n\nStreaming services like Netflix, Amazon Prime Video, Disney+ Hotstar, and regional-specific platforms like Aha have democratized content distribution, providing alternatives to traditional theatrical releases. This shift became particularly pronounced during the COVID-19 pandemic when theaters were closed, forcing the industry to adapt.\n\nOTT platforms have fostered content-driven cinema, allowing filmmakers to explore unconventional narratives and themes that might not be commercially viable for theatrical releases. Films like 'Cinema Bandi' (Netflix) and 'Mail' (Aha) found appreciative audiences on streaming platforms, while they might have struggled in theaters.\n\nThese platforms have also expanded the global reach of Telugu content. Films and series are now accessible to international audiences and the Telugu diaspora worldwide, breaking geographical constraints of traditional distribution models.\n\nAdditionally, OTT has created demand for Telugu web series, a format previously unexplored by the industry. Shows like 'Loser', 'Chadarangam', and 'Parampara' have demonstrated the potential of long-format storytelling in Telugu.\n\nAs the industry continues to evolve, a hybrid model is emerging, where theatrical releases and OTT platforms coexist, each serving different types of content and audience preferences.",
            tags = listOf("OTT", "Streaming", "Digital", "Netflix", "Amazon", "Aha", "Telugu Cinema", "Web Series"),
            isLikedByUser = false
        )
    )

    override suspend fun getArticles(
        limit: Int?,
        lastKey: String?,
        postedBy: String?,
        tags: String?,
        sortBy: String?,
        order: String?,
        userId: String?
    ): ArticlesResponse {
        // If mock data is enabled, return mock articles
        if (useMockData) {
            // Filter articles based on parameters
            var filteredArticles = mockArticles

            // Filter by postedBy if provided
            postedBy?.let { authorFilter ->
                filteredArticles = filteredArticles.filter { it.postedBy == authorFilter }
            }

            // Filter by tags if provided
            tags?.let { tagFilter ->
                val tagsList = tagFilter.split(",")
                filteredArticles = filteredArticles.filter { article ->
                    article.tags?.any { it in tagsList } ?: false
                }
            }

            // Sort articles
            val defaultSortBy = "createdAt"
            val defaultOrder = "desc"

            filteredArticles = when (sortBy ?: defaultSortBy) {
                "likes" -> {
                    if ((order ?: defaultOrder) == "desc") {
                        filteredArticles.sortedByDescending { it.likes }
                    } else {
                        filteredArticles.sortedBy { it.likes }
                    }
                }
                "createdAt" -> {
                    if ((order ?: defaultOrder) == "desc") {
                        filteredArticles.sortedByDescending { it.createdAt }
                    } else {
                        filteredArticles.sortedBy { it.createdAt }
                    }
                }
                else -> filteredArticles // No sorting
            }

            // Handle pagination using lastKey
            lastKey?.let { key ->
                val lastArticleIndex = filteredArticles.indexOfFirst { it.id == key }
                if (lastArticleIndex != -1 && lastArticleIndex < filteredArticles.size - 1) {
                    filteredArticles = filteredArticles.subList(lastArticleIndex + 1, filteredArticles.size)
                } else {
                    filteredArticles = emptyList()
                }
            }

            // Apply limit if provided
            val limitValue = limit ?: 10
            val paginatedArticles = if (filteredArticles.size > limitValue) {
                filteredArticles.take(limitValue)
            } else {
                filteredArticles
            }

            // Determine next key for pagination
            val nextKey = if (paginatedArticles.size < limitValue || paginatedArticles.size >= filteredArticles.size) {
                // No more data available
                null
            } else {
                // Use the last article's ID as the next key
                paginatedArticles.lastOrNull()?.id
            }

            return ArticlesResponse(
                success = true,
                data = ArticlesData(
                    items = paginatedArticles,
                    nextKey = nextKey
                )
            )
        }

        // If mock data is not enabled, use the actual API service
        return retrofitArticlesApiService.getArticles(
            limit = limit,
            lastKey = lastKey,
            postedBy = postedBy,
            tags = tags,
            sortBy = sortBy,
            order = order,
            userId = userId
        )
    }

    override suspend fun likeArticle(request: ArticleLikeRequest): ArticleLikeResponse {
        if (useMockData) {
            // For mock implementation, just return success
            return ArticleLikeResponse(
                success = true,
                message = "Article liked successfully"
            )
        }

        return retrofitArticlesApiService.likeArticle(request)
    }

    override suspend fun unlikeArticle(request: ArticleLikeRequest): ArticleLikeResponse {
        if (useMockData) {
            // For mock implementation, just return success
            return ArticleLikeResponse(
                success = true,
                message = "Article unliked successfully"
            )
        }

        return retrofitArticlesApiService.unlikeArticle(request)
    }
}
